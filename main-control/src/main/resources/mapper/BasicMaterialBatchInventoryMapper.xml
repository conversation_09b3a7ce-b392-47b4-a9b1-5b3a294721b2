<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.basicData.BasicMaterialBatchInventoryMapper">

    <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.domain.basicData.BasicMaterialBatchInventory">
        update basic_material_batch_inventory
        <set>
            <if test="containerCode != null and containerCode != ''">
                container_code = #{containerCode,jdbcType=VARCHAR},
            </if>
            <if test="materialCode != null and materialCode != ''">
                material_code = #{materialCode,jdbcType=VARCHAR},
            </if>
            <if test="materialNum != null">
                material_num = #{materialNum,jdbcType=INTEGER},
            </if>
            <if test="freezeNum != null">
                freeze_num = #{freezeNum,jdbcType=INTEGER},
            </if>
            <if test="availNum != null">
                avail_num = #{availNum,jdbcType=INTEGER},
            </if>
            <if test="inDate != null">
                in_date = #{inDate,jdbcType=DATE},
            </if>
            <if test="produceDate != null">
                produce_date = #{produceDate,jdbcType=DATE},
            </if>
            <if test="batch != null and batch != ''">
                batch = #{batch,jdbcType=VARCHAR},
            </if>
            <if test="upperIndex != null and upperIndex != ''">
                upper_index = #{upperIndex,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="isIsolation != null">
                is_isolation = #{isIsolation,jdbcType=INTEGER},
            </if>
            <if test="checkNum != null">
                check_num = #{checkNum,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="queryBasicMaterialWarehouseInfo" resultType="com.ruoyi.vo.warehouse.BasicMaterialDetailDto">
        SELECT
            bwi.warehouse_name AS warehouse_name,
            bwi.warehouse_code AS warehouse_code,
            bmbi.material_code as material_code,
            COALESCE(SUM( bmbi.material_num), 0) AS total_num,
            COALESCE(SUM( bmbi.freeze_num), 0) AS freeze_num,
            COALESCE(SUM( bmbi.avail_num), 0) AS avail_num
        FROM
            basic_material_batch_inventory bmbi
                JOIN
            basic_warehouse_container bwc ON bmbi.container_code = bwc.container_code
                JOIN
            basic_warehouse_location bwl ON bwc.location_code = bwl.location_code
                JOIN
            basic_warehouse_info bwi ON bwl.warehouse_code = bwi.warehouse_code
        WHERE
        1=1
        <if test="keyWord != null and keyWord.trim() != ''">
            AND bmbi.material_code = #{keyWord}
        </if>
        GROUP BY
            bwi.warehouse_name, bwi.warehouse_code
    </select>
    <select id="queryBasicMaterialInventory"
            resultType="com.ruoyi.vo.warehouse.BasicMaterialBatchInventoryDto">
        SELECT
        bwl.location_code,
        bmi.material_name,
        bmi.material_img,
        bmbi.*
        FROM
        basic_material_batch_inventory bmbi
        JOIN
        basic_warehouse_container bwc ON bmbi.container_code = bwc.container_code
        JOIN
        basic_material_info bmi ON bmbi.material_code = bmi.material_code
        JOIN
        basic_warehouse_location bwl ON bwc.location_code = bwl.location_code
        WHERE
        1=1
        <if test="keyWord != null and keyWord.trim() != ''">
            AND bwl.warehouse_code = #{keyWord}
        </if>
        <if test="keySubWord != null and keySubWord.trim() != ''">
            AND (bmbi.material_code = #{keySubWord} OR bmi.material_name LIKE CONCAT('%', #{keySubWord}, '%'))
        </if>
        <if test = "state != null">
            and bmbi.avail_num &gt; 0
        </if>
           order by bmbi.produce_date asc
    </select>
    <select id="queryContainerMaterialInfo"
            resultType="com.ruoyi.vo.warehouse.MaterialContainerInfoDto">
        select
        bmbi.id as inventory_id,
        bmbi.material_code,
        bmbi.container_code,
        bmi.material_name,
        bmi.material_img,
        bmi.material_sort,
        bmi.expiry_date,
        bmc.classify_name,
        bwl.node_name as position_name,
        a.node_name as level_name,
        b.node_name as shelf_name,
        bwi.warehouse_name,
        bmbi.material_num,
        bmbi.freeze_num,
        bmbi.avail_num,
        bmbi.in_date,
        bmbi.upper_index,
        bmbi.produce_date,
        bmbi.batch,
        bmbi.remark,
        bmbi.is_isolation,
        bmbi.check_num
        from
        basic_material_batch_inventory bmbi
        left join
        basic_warehouse_container bwc on
        bmbi.container_code = bwc.container_code
        left join
        basic_material_info bmi on
        bmi.material_code = bmbi.material_code
        left join
        basic_warehouse_location bwl on
        bwc.location_code = bwl.location_code
        left join
        basic_warehouse_location a on
        a.id = bwl.parent_id
        left join
        basic_warehouse_location b on
        b.id = a.parent_id
        left join
        basic_warehouse_info bwi on
        bwl.warehouse_code = bwi.warehouse_code
        left join basic_material_classify bmc on bmc.classify_code = bmi.classify_code
        where
        1 = 1
        and bmbi.avail_num &gt; 0
        <if test="queryParamVO.keyWord != null and queryParamVO.keyWord.trim() != ''">
            AND (bmbi.material_code LIKE CONCAT('%', #{queryParamVO.keyWord}, '%') OR bmi.material_name LIKE CONCAT('%', #{queryParamVO.keyWord}, '%'))
        </if>
        <if test="queryParamVO.keySubWord != null and queryParamVO.keySubWord.trim() != ''">
            AND bwi.warehouse_name = #{queryParamVO.keySubWord}
        </if>
        <if test="queryParamVO.keyThirdWord != null and queryParamVO.keyThirdWord.trim() != ''">
            and FIND_IN_SET(bmbi.upper_index, #{queryParamVO.keyThirdWord}) > 0
        </if>

        <if test = "queryParamVO.state != null">
            and bmi.material_sort = #{queryParamVO.state}
        </if>
        <if test="materialCodes != null">
            and bmbi.material_code IN
            <foreach collection="materialCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        order by bwi.warehouse_name desc,shelf_name desc,position_name desc,bmbi.container_code desc,bmbi.in_date desc
    </select>
    <select id="selectByContainerCode" resultType="com.ruoyi.domain.basicData.BasicMaterialBatchInventory">
        select *
        from basic_material_batch_inventory
        where 1 = 1
          and container_code = #{containerCode}
          and material_num &gt; 0
    </select>
    <select id="qryCountByMaterialCode" resultType="java.lang.Integer">
        select
            count(*)
        from
            basic_material_batch_inventory
        where
            1=1
          and material_code = #{materialCode}
    </select>
    <select id="queryInventoryByCode" resultType="com.ruoyi.vo.warehouse.MaterialInventoryDto">
        SELECT
            COALESCE(SUM(material_num), 0) AS material_num,
            COALESCE(SUM(freeze_num), 0) AS freeze_num,
            COALESCE(SUM(avail_num), 0) AS avail_num
        FROM
            basic_material_batch_inventory
        WHERE
            1=1
          AND material_code = #{material_code}
    </select>
    <select id="getAvailBatchByMaterialCode"
            resultType="com.ruoyi.domain.basicData.BasicMaterialBatchInventory">
        SELECT
            *
        FROM
            basic_material_batch_inventory
        WHERE
            1=1
          AND material_code = #{materialCode}
          and avail_num &gt; 0
        order by in_date asc
    </select>
    <select id="queryMaterialNumDate" resultType="com.ruoyi.vo.warehouse.BasicMaterialNumInfo">
        SELECT
        bmi.id,
        bmi.material_code,
        bmi.material_name,
        bmi.classify_code,
        bmi.material_sort,
        bmi.specifications,
        bmi.produce_unit,
        bmi.min_inventory,
        bmi.max_inventory,
        bmi.material_img,
        COALESCE(SUM(bmbi.material_num), 0) AS material_num,
        COALESCE(SUM(bmbi.freeze_num), 0) AS freeze_num,
        COALESCE(SUM(bmbi.avail_num), 0) AS avail_num,
        bmc.classify_name
        FROM
        basic_material_info bmi
        LEFT JOIN
        basic_material_batch_inventory bmbi ON bmi.material_code = bmbi.material_code
        LEFT JOIN
        basic_material_classify bmc ON bmc.classify_code = bmi.classify_code
        where 1 = 1
        <if test="keyWord != null and keyWord.trim() != ''">
            AND (bmi.material_code = #{keyWord} OR bmi.material_name LIKE CONCAT('%', #{keyWord}, '%'))
        </if>
        <if test="keySubWord != null and keySubWord.trim() != ''">
            AND FIND_IN_SET(bmi.material_sort, #{keySubWord}) &gt; 0
        </if>
        <if test="state != null and state == 0">
            and avail_num > 0
        </if>
        GROUP BY
        bmi.material_code,
        bmi.material_name,
        bmi.classify_code,
        bmi.material_sort,
        bmi.specifications,
        bmi.produce_unit,
        bmi.min_inventory,
        bmi.max_inventory,
        bmi.material_img,
        bmc.classify_name order by bmi.classify_code desc
    </select>
    <select id="queryMaterialAlertInfo" resultType="com.ruoyi.vo.warehouse.BasicMaterialAlertInfo">
        SELECT
        bmi.*,
        bmc.classify_name,
        SUM(bmbi.material_num) AS material_num,
        CASE
        WHEN SUM(bmbi.material_num) &lt; bmi.min_inventory THEN 0
        WHEN SUM(bmbi.material_num) &gt; bmi.max_inventory THEN 1
        ELSE NULL
        END AS alert_type
        FROM
        basic_material_info bmi
        JOIN
        basic_material_batch_inventory bmbi ON
        bmi.material_code = bmbi.material_code
        left join basic_material_classify bmc on bmc.classify_code = bmi.classify_code
        where 1=1
        <if test="keyWord != null and keyWord.trim() != ''">
            AND (bmi.material_code LIKE CONCAT('%', #{keyWord}, '%') OR bmi.material_name LIKE CONCAT('%', #{keyWord}, '%'))
        </if>
        <if test="state != null">
            AND bmi.material_sort = #{state}
        </if>
        GROUP BY
        bmi.material_code,
        bmi.min_inventory,
        bmi.max_inventory,
        bmc.classify_name
        HAVING
        alert_type IS NOT NULL
        <if test="stateSub != null">
            AND alert_type = #{stateSub}
        </if>
    </select>
    <select id="queryMaterialExpirationAlert" resultType="com.ruoyi.vo.warehouse.MaterialAlertInfo">
        SELECT
        bmi.material_code,
        bmi.material_name,
        bmi.classify_code,
        bmi.material_sort,
        bmi.specifications,
        bmi.produce_unit,
        bmi.expiry_date,
        bmi.material_img,
        bmbi.container_code,
        bmbi.in_date,
        bmbi.produce_date,
        bmbi.batch,
        bmc.classify_name,
        bmbi.material_num,
        bmbi.upper_index,
        DATE_ADD(bmbi.produce_date, INTERVAL bmi.expiry_date MONTH) AS expiration_date,
        CASE
        WHEN CURDATE() > DATE_ADD(bmbi.produce_date, INTERVAL bmi.expiry_date MONTH) THEN
        DATEDIFF(CURDATE(), DATE_ADD(bmbi.produce_date, INTERVAL bmi.expiry_date MONTH))
        ELSE
        0
        END AS days_overdue
        FROM
        basic_material_batch_inventory bmbi
        JOIN
        basic_material_info bmi ON
        bmi.material_code = bmbi.material_code
        left join basic_material_classify bmc on bmc.classify_code = bmi.classify_code
        WHERE
        1=1
        and CURDATE() > DATE_ADD(bmbi.produce_date, INTERVAL bmi.expiry_date MONTH)
        <if test="keyWord != null and keyWord.trim() != ''">
            AND (bmi.material_code LIKE CONCAT('%', #{keyWord}, '%') OR bmi.material_name LIKE CONCAT('%', #{keyWord},
            '%'))
        </if>
        <if test="keySubWord != null and keySubWord.trim() != ''">
            AND bmbi.container_code = #{keySubWord}
        </if>
        <if test="state != null">
            AND bmi.material_sort = #{state}
        </if>
        ORDER BY
        days_overdue DESC
    </select>
    <select id="qryNumByMaterialCode" resultType="java.lang.Integer">
        SELECT
        COALESCE(SUM(material_num), 0) AS material_num
        FROM
        basic_material_batch_inventory
        where 1 = 1
        AND material_code = #{materialCode}
    </select>

    <!-- 优化查询：一次性获取物料信息及其可用容器列表（包含位置信息） -->
    <select id="queryMaterialWithContainersOptimized" resultType="com.ruoyi.vo.warehouse.MaterialContainerInfoDto">
        SELECT
            bmbi.id as inventory_id,
            bmi.material_code,
            bmi.material_name,
            bmi.classify_code,
            bmi.material_sort,
            bmi.specifications,
            bmi.produce_unit,
            bmi.min_inventory,
            bmi.max_inventory,
            bmi.material_img,
            bmc.classify_name,
            bmbi.container_code,
            bmbi.material_num,
            bmbi.freeze_num,
            bmbi.avail_num,
            bmbi.in_date,
            bmbi.produce_date,
            bmbi.batch,
            bmbi.upper_index,
            bmbi.is_isolation,
            bmbi.check_num,
            bmbi.remark,
            bwl.node_name as position_name,
            level_loc.node_name as level_name,
            shelf_loc.node_name as shelf_name,
            bwi.warehouse_name
        FROM
            basic_material_info bmi
        INNER JOIN
            basic_material_batch_inventory bmbi ON bmi.material_code = bmbi.material_code
        LEFT JOIN
            basic_material_classify bmc ON bmc.classify_code = bmi.classify_code
        LEFT JOIN
            basic_warehouse_container bwc ON bmbi.container_code = bwc.container_code
        LEFT JOIN
            basic_warehouse_location bwl ON bwc.location_code = bwl.location_code
        LEFT JOIN
            basic_warehouse_location level_loc ON level_loc.id = bwl.parent_id
        LEFT JOIN
            basic_warehouse_location shelf_loc ON shelf_loc.id = level_loc.parent_id
        LEFT JOIN
            basic_warehouse_info bwi ON bwl.warehouse_code = bwi.warehouse_code
        WHERE
            1 = 1
            AND bmbi.avail_num > 0
        <if test="keyWord != null and keyWord.trim() != ''">
            AND (bmi.material_code = #{keyWord} OR bmi.material_name LIKE CONCAT('%', #{keyWord}, '%'))
        </if>
        ORDER BY
            bmi.material_code, bmbi.create_time ASC
    </select>

    <!-- 根据物料编码列表优化查询：一次性获取物料信息及其可用容器列表（包含位置信息） -->
    <select id="queryMaterialWithContainersByCodesOptimized" resultType="com.ruoyi.vo.warehouse.MaterialContainerInfoDto">
        SELECT
            bmbi.id as inventory_id,
            bmi.material_code,
            bmi.material_name,
            bmi.classify_code,
            bmi.material_sort,
            bmi.specifications,
            bmi.produce_unit,
            bmi.min_inventory,
            bmi.max_inventory,
            bmi.material_img,
            bmc.classify_name,
            bmbi.container_code,
            bmbi.material_num,
            bmbi.freeze_num,
            bmbi.avail_num,
            bmbi.in_date,
            bmbi.produce_date,
            bmbi.batch,
            bmbi.upper_index,
            bmbi.is_isolation,
            bmbi.check_num,
            bmbi.remark,
            bwl.node_name as position_name,
            level_loc.node_name as level_name,
            shelf_loc.node_name as shelf_name,
            bwi.warehouse_name
        FROM
            basic_material_info bmi
        INNER JOIN
            basic_material_batch_inventory bmbi ON bmi.material_code = bmbi.material_code
        LEFT JOIN
            basic_material_classify bmc ON bmc.classify_code = bmi.classify_code
        LEFT JOIN
            basic_warehouse_container bwc ON bmbi.container_code = bwc.container_code
        LEFT JOIN
            basic_warehouse_location bwl ON bwc.location_code = bwl.location_code
        LEFT JOIN
            basic_warehouse_location level_loc ON level_loc.id = bwl.parent_id
        LEFT JOIN
            basic_warehouse_location shelf_loc ON shelf_loc.id = level_loc.parent_id
        LEFT JOIN
            basic_warehouse_info bwi ON bwl.warehouse_code = bwi.warehouse_code
        WHERE
            1 = 1
            AND bmbi.avail_num > 0
        <if test="materialCodes != null and materialCodes.size() > 0">
            AND bmi.material_code IN
            <foreach collection="materialCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        ORDER BY
            bmi.material_code, bmbi.create_time ASC
    </select>


</mapper>