package com.ruoyi.service.work;

import com.alibaba.excel.EasyExcel;
import com.google.common.net.HttpHeaders;
import com.ruoyi.utils.DateAndTimeUtil;
import org.springframework.stereotype.Service;
import org.springframework.http.MediaType;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * Excel导出服务
 */
@Service
public class ExcelExportService {
    /**
     * 导出数据到Excel
     * @param response HTTP响应对象
     * @param dataList 要导出的数据集合
     * @param fileName 导出的文件名
     * @param clazz 数据对象的类类型
     * @param <T> 泛型，代表数据对象类型
     * @throws IOException IO异常
     */
    public <T> void exportExcel(HttpServletResponse response, List<T> dataList,
                                String fileName, Class<T> clazz) throws IOException {
        // 设置响应头信息
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE + ";charset=utf-8");
        response.setCharacterEncoding("utf-8");

        // 处理文件名，防止中文乱码
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8")
                .replaceAll("\\+", "%20");
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

        // 使用EasyExcel写入数据
        EasyExcel.write(response.getOutputStream(), clazz)
                .sheet("数据列表" + DateAndTimeUtil.getNowTime())  // 工作表名称
                .doWrite(dataList);
    }
}
