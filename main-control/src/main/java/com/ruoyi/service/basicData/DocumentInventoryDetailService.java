package com.ruoyi.service.basicData;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.LocationUtils;
import com.ruoyi.domain.basicData.DocumentInventoryDetail;
import com.ruoyi.mapper.basicData.DocumentInventoryDetailMapper;
import com.ruoyi.utils.DocumentInventoryDetailQueryVO;
import com.ruoyi.vo.basicData.DocumentInventoryDetailDto;
import com.ruoyi.vo.warehouse.ContainerLocationInfoDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

@Service
public class DocumentInventoryDetailService extends ServiceImpl<DocumentInventoryDetailMapper, DocumentInventoryDetail> {

    @Resource
    private DocumentInventoryDetailMapper documentInventoryDetailMapper;

    public List<DocumentInventoryDetailDto> queryDocumentInventoryDetail(DocumentInventoryDetailQueryVO queryParamVO) {
        List<DocumentInventoryDetailDto> documentInventoryDetails = documentInventoryDetailMapper.queryDocumentInventoryDetail(queryParamVO);

        // 使用LocationUtils工具类格式化位置信息
        for (DocumentInventoryDetailDto documentInventoryDetailDto : documentInventoryDetails) {
            // 创建位置信息DTO
            ContainerLocationInfoDto locationInfo = new ContainerLocationInfoDto();
            locationInfo.setWarehouseName(documentInventoryDetailDto.getWarehouseName());
            locationInfo.setShelfName(documentInventoryDetailDto.getShelfName());
            locationInfo.setLevelName(documentInventoryDetailDto.getLevelName());
            locationInfo.setPositionName(documentInventoryDetailDto.getPositionName());
            // 使用LocationUtils格式化位置信息
            String formattedLocation = LocationUtils.formatLocation(locationInfo);
            documentInventoryDetailDto.setLocation(formattedLocation);
        }
        return documentInventoryDetails;
    }

    public void saveObjData(DocumentInventoryDetail documentInventoryDetail) {
        documentInventoryDetail.setId(UUID.randomUUID().toString());
        this.documentInventoryDetailMapper.insert(documentInventoryDetail);
    }
}
