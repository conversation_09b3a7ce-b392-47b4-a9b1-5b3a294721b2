package com.ruoyi.service.qc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.LocalStringUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.qc.QcIpqcDetailInfo;
import com.ruoyi.domain.qc.QcIpqcTaskInfo;
import com.ruoyi.domain.qc.QcTemplateDetail;
import com.ruoyi.mapper.qc.QcIpqcDetailInfoMapper;
import com.ruoyi.utils.AutoNum;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.qc.QcIpqcDetailInfoVo;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: lhb
 * @CreateDate: 2025/6/24 14:31
 * @Description: 类描述
 */
@Service
public class QcIpqcDetailInfoService extends ServiceImpl<QcIpqcDetailInfoMapper, QcIpqcDetailInfo> {

    @Resource
    private QcIpqcDetailInfoMapper qcIpqcDetailInfoMapper;
    @Resource
    private QcIpqcInfoService qcIpqcInfoService;
    /**
     * 新增
     */
    public ResponseResult addQcIpqcDetailInfo(QcIpqcDetailInfo qcIpqcDetailInfo){
        qcIpqcDetailInfo.setId(LocalStringUtils.getDataUUID());
        qcIpqcDetailInfo.setCreateTime(new Date());
        qcIpqcDetailInfo.setUpdateTime(new Date());
        int result = this.qcIpqcDetailInfoMapper.insert(qcIpqcDetailInfo);
        if(result >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("保存数据失败，请重试");
    }

    /**
     * 删除
     */
    public ResponseResult deleteQcIpqcDetailInfo(BatchIdsReq req) {
        int count = this.qcIpqcDetailInfoMapper.deleteBatchIds(req.getIds());
        if(count >= req.getIds().size()){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("部分数据删除失败，请重试");
    }

    /**
     * 更新质检详情
     */
    public ResponseResult uptQcIpqcDetailInfo(QcIpqcDetailInfo qcIpqcDetailInfo) {
        qcIpqcDetailInfo.setUpdateTime(new Date());
        int count = this.qcIpqcDetailInfoMapper.updateById(qcIpqcDetailInfo);
        if(count >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("数据更新失败，请重试");
    }

    /**
     * 批量更新质检详情
     * @param qcIpqcDetailInfos 待更新的质检详情列表
     * @return 操作结果
     */
    public ResponseResult batchUpdateQcIpqcDetailInfo(List<QcIpqcDetailInfo> qcIpqcDetailInfos) {
        Date now = new Date();
        qcIpqcDetailInfos.forEach(item -> item.setUpdateTime(now));
        boolean success = this.updateBatchById(qcIpqcDetailInfos);

        return success ? ResponseResult.getSuccessResult()
                : ResponseResult.getErrorResult("批量更新失败");
    }

    /**
     * 分页
     */
    public List<QcIpqcDetailInfoVo> queryQcIpqcDetailInfo(QueryParamVO param) {
        return this.qcIpqcDetailInfoMapper.queryQcIpqcDetailInfo(param);
    }


    /**
     * 通过质检编码集合、质检项目 查询相关的质检详情
     */
    public List<QcIpqcDetailInfo> queryQcIpqcDetailInfoByCodes(List<String> ipqcCodeList,String itemCode) {
        LambdaQueryWrapper<QcIpqcDetailInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(QcIpqcDetailInfo::getIpqcCode, ipqcCodeList);
        if (StringUtils.isNotBlank(itemCode)) {
            wrapper.eq(QcIpqcDetailInfo::getItemCode, itemCode);
        }
        return qcIpqcDetailInfoMapper.selectList(wrapper);
    }


    public List<QcIpqcDetailInfo> queryQcIpqcDetailByDetailId(String template_detail_id) {
        LambdaQueryWrapper<QcIpqcDetailInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(QcIpqcDetailInfo::getTemplateDetailId, template_detail_id);
        return qcIpqcDetailInfoMapper.selectList(wrapper);
    }
    /**
     * 模板改动，删除了相关的质检项目
     * @param template_detail_id
     */
    public void deleteQualityItems(String template_detail_id){
        List<QcIpqcDetailInfo> qcIpqcDetailInfos = queryQcIpqcDetailByDetailId(template_detail_id);
        List<String> deleteIds = new ArrayList<>();
        for (QcIpqcDetailInfo qcIpqcDetailInfo :qcIpqcDetailInfos){
            QcIpqcTaskInfo qcIpqcTaskInfo = qcIpqcInfoService.queryQcIpqcInfoByIpqcCode(qcIpqcDetailInfo.getIpqcCode());
            if(qcIpqcTaskInfo != null && qcIpqcTaskInfo.getStatus() != CommonConstant.QcTaskStatus.COMPLETED){
                deleteIds.add(qcIpqcDetailInfo.getId());
            }
        }
        qcIpqcDetailInfoMapper.deleteBatchIds(deleteIds);
    }

    /**
     * 模板改动，新增质检项目
     */
    public void addQualityItems(String templateCode, QcTemplateDetail qcTemplateDetail) {
        List<QcIpqcTaskInfo> qcIpqcTaskInfos = qcIpqcInfoService.queryQcIpqcInfoByTemplateCode(templateCode);
        for (QcIpqcTaskInfo qcIpqcTaskInfo : qcIpqcTaskInfos) {
            if (qcIpqcTaskInfo != null && qcIpqcTaskInfo.getStatus() != CommonConstant.QcTaskStatus.COMPLETED) {
                QcIpqcDetailInfo qcIpqcDetailInfo = new QcIpqcDetailInfo();
                qcIpqcDetailInfo.setIpqcCode(qcIpqcTaskInfo.getIpqcCode());
                qcIpqcDetailInfo.setCreateBy(qcIpqcTaskInfo.getCreateBy());
                qcIpqcDetailInfo.setTemplateDetailId(qcTemplateDetail.getId());
                qcIpqcDetailInfo.setItemCode(qcTemplateDetail.getItemCode());
                addQcIpqcDetailInfo(qcIpqcDetailInfo);
            }
        }

    }
}
