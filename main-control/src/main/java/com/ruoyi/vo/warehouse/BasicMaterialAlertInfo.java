package com.ruoyi.vo.warehouse;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

/**
 * 物料预警信息
 */
@Data
public class BasicMaterialAlertInfo {

    @ExcelProperty(value = "物料编码", index = 0)
    private String materialCode;

    @ExcelProperty(value = "物料名称", index = 1)
    private String materialName;

    @ExcelProperty(value = "分类编码", index = 2)
    private String classifyCode;

    @ExcelProperty(value = "分类名称", index = 3)
    private String classifyName;

    @ExcelProperty(value = "规格型号", index = 4)
    private String specifications;

    @ExcelProperty(value = "生产单位", index = 5)
    private String produceUnit;

    @ExcelProperty(value = "最低库存", index = 6)
    private Integer minInventory;

    @ExcelProperty(value = "最高库存", index = 7)
    private Integer maxInventory;

    @ExcelProperty(value = "物料数量", index = 8)
    private Integer materialNum;

    @ExcelProperty(value = "有效期(月)", index = 9)
    private Integer expiryDate;

    /**
     *预警类型
     * 0:缺储预警,1:超储预警,2:临期预警
     */
//    @Excel(name = "预警类型", readConverterExp = "0=缺储预警,1=超储预警,2=临期预警", sort = 4)
    @ExcelProperty(value = "预警类型",index = 10)
    private Integer alertType;

    @ExcelProperty(value = "物料图片",index = 11)
    private String materialImg;
}
