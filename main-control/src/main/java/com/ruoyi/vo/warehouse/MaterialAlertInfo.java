package com.ruoyi.vo.warehouse;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 物料过期预警信息
 */
@Data
public class MaterialAlertInfo {

    @ExcelProperty(value = "物料编码", index = 0)
    private String materialCode;

    @ExcelProperty(value = "物料名称", index = 1)
    private String materialName;

    @ExcelProperty(value = "过期天数", index = 2)
    private Integer daysOverdue;

    @ExcelProperty(value = "分类编码", index = 3)
    private String classifyCode;

    @ExcelProperty(value = "分类名称", index = 4)
    private String classifyName;

    @ExcelProperty(value = "物料类型", index = 5)
    private Integer materialSort;

    @ExcelProperty(value = "规格型号", index = 6)
    private String specifications;

    @ExcelProperty(value = "物料单位", index = 7)
    private String produceUnit;

    @ExcelProperty(value = "物料数量", index = 8)
    private Integer materialNum;

    @ExcelProperty(value = "有效期(月)", index = 9)
    private Integer expiryDate;

    @ExcelProperty(value = "容器编码", index = 10)
    private String containerCode;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ExcelProperty(value = "入库日期", index = 11)
    private String inDate;

    @ExcelProperty(value = "入库编码", index = 12)
    private String upperIndex;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ExcelProperty(value = "生产日期", index = 13)
    private String produceDate;

    @ExcelProperty(value = "批次", index = 14)
    private String batch;

    @ExcelProperty(value = "物料图片", index = 15)
    private String materialImg;

    @ExcelProperty(value = "仓库名称", index = 16)
    private String materialLocation;
}
