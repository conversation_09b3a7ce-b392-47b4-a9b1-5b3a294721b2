package com.ruoyi.vo.qc;

import lombok.Data;

/**
 * @Author: lhb
 * @CreateDate: 2025/6/24 22:22
 * @Description: 类描述
 */
@Data
public class HandIpqcDetail {

    private String id;
    /**
     * 致命缺陷数量
     */
    private Integer crQuantity;
    /**
     * 严重缺陷数量
     */
    private Integer majQuantity;
    /**
     * 轻微缺陷数量
     */
    private Integer minQuantity;
    /**
     * 质检值
     */
    private String qcVal;
    /**
     * 备注
     */
    private String remark;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 项目检测结果 1 合格 2 不合格
     */
    private Integer projectCheckResult;
}
