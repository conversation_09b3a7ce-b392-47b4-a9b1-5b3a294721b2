package com.ruoyi.vo.qc;

import lombok.Data;

import java.util.List;

/**
 * @Author: lhb
 * @CreateDate: 2025/6/24 22:12
 * @Description: 人工质检
 */
@Data
public class BatchHandIpqcInfo {

    private String id;
    /**
     * 质检任务
     */
    private List<String> ipqcCodeList;
    /**
     * 检测总数量
     */
    private Integer totalCheckNum;
    /**
     * 不合格数
     */
    private Integer totalUnqualifiedNum;
    /**
     * 合格品数量
     */
    private Integer totalQualifiedNum;
    /**
     * 质检结果 (1 通过 2 不通过)
     */
    private Integer checkResult;
    /**
     * 状态 1 保存 3暂存 4免检
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 更新者
     */
    private String updateBy;
    /**
     * 让步结果： 1允许 2不允许
     */
    private Integer concedeResult;
    /**
     * 详情
     */
    private List<HandIpqcDetail> details;

}
