package com.ruoyi.vo.report;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.utils.converter.BoundConverter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 物料进出记录查询实体
 */
@Data
public class RecordMaterialInoutVo {
    @TableId(type = IdType.ASSIGN_UUID)
    @ExcelProperty(value = "数据主键", index = 0)
    private String id;

    @ExcelProperty(value = "单据编码" ,index = 1)
    private String boundIndex;

    /**
     * 单据类型，1:生产领料 2:生产补料 3:生产入库（成品入库） 4:生产退料 5采购入库  6采购退货出库  7销售出库  8销售退货入库 9物料调拨 10库存盘点 11仓库用料
     */
    @ExcelProperty(value = "单据类型" ,converter = BoundConverter.class,index = 2)
    private Integer boundType;

    /**
     * 数据来源0页面，1MES，2ERP
     */
    @ExcelProperty(value = "数据来源" ,index = 3)
    private Integer dataOrigin;

    /**
     * 出入类型 0：入库，1：出库，2：冻结，3：解冻
     */
    @ExcelProperty(value = "出入类型" ,index = 4)
    private Integer inoutType;

    @ExcelProperty(value = "物料编码" ,index = 5)
    private String materialCode;

    @ExcelProperty(value = "物料名称" ,index = 6)
    private String materialName;

    @ExcelProperty(value = "物料数量" ,index = 7)
    private Integer totalNum;

    @ExcelProperty(value = "容器编码" ,index = 8)
    private String containerCode;

    @ExcelProperty(value = "批次" ,index = 9)
    private String batch;

//    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ExcelProperty(value = "生产日期" ,index = 10)
    private String produceDate;

    @ExcelProperty(value = "数据源单号" ,index = 11)
    private String upperIndex;

    @ExcelProperty(value = "备注" ,index = 12)
    private String remark;

//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "记录时间" ,index = 13)
    private String recordDate;

    @ExcelProperty(value = "锁单人" ,index = 14)
    private String recorder;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "锁单日期" ,index = 15)
    private String lockTime;

    @ExcelProperty(value = "物料位置" ,index = 16)
    private String materialLocation;

    @ExcelProperty(value = "物料图片" ,index = 17)
    private String materialImg;

}