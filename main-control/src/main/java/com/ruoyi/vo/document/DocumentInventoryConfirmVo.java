package com.ruoyi.vo.document;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 单据出入库确认请求VO
 * 用于确认本次出入库的实际数量
 * 
 * <AUTHOR>
 */
@Data
public class DocumentInventoryConfirmVo {
    
    /**
     * 出入库类型：0-入库，1-出库
     */
    @NotNull(message = "出入库类型不能为空")
    private Integer type;
    
    /**
     * 批次确认明细列表
     */
    @NotEmpty(message = "确认明细列表不能为空")
    @Valid
    private List<DocumentInventoryItemVo> items;
}
