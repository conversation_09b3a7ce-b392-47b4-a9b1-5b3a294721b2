package com.ruoyi.utils;

import java.util.HashMap;
import java.util.Map;

public class StatusMapping {
    // 数值到中文的映射（导出时用）
    public static final Map<Integer, String> NUMBER_TO_CHINESE = new HashMap<>();
    // 中文到数值的映射（导入时用）
    public static final Map<String, Integer> CHINESE_TO_NUMBER = new HashMap<>();

    static {
        // 初始化映射关系：单据类型，1:生产领料 2:生产补料 3:生产入库（成品入库） 4:生产退料 5采购入库  6采购退货出库  7销售出库  8销售退货入库 9物料调拨 10库存盘点 11仓库用料
        NUMBER_TO_CHINESE.put(1, "生产领料");
        NUMBER_TO_CHINESE.put(2, "生产补料");
        NUMBER_TO_CHINESE.put(3, "生产入库（成品入库）");
        NUMBER_TO_CHINESE.put(4, "生产退料");
        NUMBER_TO_CHINESE.put(5, "采购入库");
        NUMBER_TO_CHINESE.put(6, "采购退货出库");
        NUMBER_TO_CHINESE.put(7, "销售出库");
        NUMBER_TO_CHINESE.put(8, "销售退货入库");
        NUMBER_TO_CHINESE.put(9, "物料调拨");
        NUMBER_TO_CHINESE.put(10, "仓库用料");
        // 反向映射（中文→数值）
        NUMBER_TO_CHINESE.forEach((k, v) -> CHINESE_TO_NUMBER.put(v, k));
    }
}
