package com.ruoyi.utils.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.ruoyi.utils.StatusMapping;

public class BoundConverter implements Converter<Integer> {
    // 导出时：数值→中文（如1→"启用"）
    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        if (value == null) {
            return new WriteCellData<>(""); // 空值处理
        }
        // 根据数值获取中文（默认返回"未知"）
        String chinese = StatusMapping.NUMBER_TO_CHINESE.getOrDefault(value, "未知");
        return new WriteCellData<>(chinese);
    }

    // 导入时：中文→数值（如"禁用"→0）
    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        String chinese = cellData.getStringValue();
        if (chinese == null || chinese.trim().isEmpty()) {
            return null; // 空值处理
        }
        // 根据中文获取数值（默认返回-1表示未知）
        return StatusMapping.CHINESE_TO_NUMBER.getOrDefault(chinese.trim(), -1);
    }

    // 支持的Java类型（Integer）
    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    // 支持的Excel单元格类型（字符串）
    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }
}
