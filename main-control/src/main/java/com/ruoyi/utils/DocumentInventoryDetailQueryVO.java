package com.ruoyi.utils;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 单据物料明细查询参数
 * 继承通用查询参数，新增单据物料明细业务相关的查询字段
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentInventoryDetailQueryVO extends QueryParamVO {

    /**
     * 仓库名称（模糊搜索）
     * 通过容器位置关联查询仓库名称
     */
    private String warehouseName;

    /**
     * 供应商/客户名称（模糊搜索）
     * 同时搜索company_code和company_name字段
     * 根据单据类型动态使用：
     * - 采购相关单据(5-采购入库, 6-采购退货出库)：搜索供应商名称（company_type=1）
     * - 销售相关单据(7-销售出库, 8-销售退货入库)：搜索客户名称（company_type=0）
     * - 生产相关单据：不使用此字段
     */
    private String supplierCustomerName;

    /**
     * 物料信息（模糊搜索）
     * 同时搜索物料编码和物料名称
     */
    private String materialInfo;

    /**
     * 批次号（模糊搜索）
     */
    private String batchCode;

    /**
     * 完成开始时间（完成时间范围查询）
     */
    private String completedStartDate;

    /**
     * 完成结束时间（完成时间范围查询）
     */
    private String completedEndDate;

    /**
     * 字段映射说明：
     * 
     * 继承自QueryParamVO的字段使用：
     * - keyWord: 单据明细编码（detail_code）- 精确匹配
     * - keySubWord: 出入库单据编码（transaction_code）- 模糊搜索
     * - keyThirdWord: 出入库单据业务类型（business_type）- 精确匹配
     *   1:生产领料 2:生产补料 3:生产入库（成品入库） 4:生产退料 
     *   5:采购入库 6:采购退货出库 7:销售出库 8:销售退货入库
     * - keyFourWord: 单据类型（transaction_type）- 精确匹配
     *   0:入库 1:出库
     * - state: 出入库状态（warehouse_status）- 精确匹配
     *   0-待确认 1-待出入库 2-出入库中 3-待生产确认 4-生产已确认 5-待仓库确认 6-仓库已确认 7-已完成
     * - stateSub: 质检状态（qc_status）- 精确匹配
     *   0-无需质检 1-待质检 2-质检中 3-质检合格 4-质检不合格 5-免检
     * - bdate: 创建开始时间
     * - edate: 创建结束时间
     */
}
