package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 *单据明细详情表
 */
@Data
@TableName("basic_document_detail")
public class BasicDocumentDetail {

    /**
     * 主键编码
     */
    private String id;

    /**
     * 父表单据编号
     */
    private String documentCode;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料计划总数量（该物料的计划处理总数量）
     * 注：同一物料的多个容器批次数量之和应等于此数量
     */
    private Integer quantity;

    /**
     * 物料已完成数量（该物料最终完成处理的数量）
     * 注：通过累加该物料所有DocumentInventoryDetail记录的completedNum得出
     */
    private Integer completedNum;

    /**
     * 物料累计确认数量（该物料所有容器所有批次的累计确认数量）
     * 入库场景：累计来料确认数量（所有批次累计确认到货，支持超计划确认）
     * 出库场景：累计备货确认数量（所有批次累计确认备货，支持分批分容器确认）
     * 注：此数量可能大于等于已完成数量，因为包含质检不合格或未通过处理的部分
     * 计算方式：每次调用confirmArrival时累加arrival.getArrivalQuantity()
     */
    private Integer totalArrivalNum;

    /**
     * 任务状态：
     * 0-待处理（单据锁定后的初始状态，入库为待入库，出库为待出库）
     * 1-处理中（部分数量已处理，入库为部分入库，出库为部分出库）
     * 2-已完成（全部数量已处理完成，入库为入库完成，出库为出库完成）
     */
    private Integer taskStatus;

    /**
     * 最新批次质检状态：
     * 0-无需质检（物料本身不需要质检）
     * 1-待质检（需要质检，等待开始质检）
     * 2-质检中（质检任务进行中）
     * 3-质检合格（质检通过，可进入下一环节）
     * 4-质检不合格（质检失败，需要处理）
     * 5-免检（有质检任务但质检结果为免检）
     */
    private Integer latestQcStatus;

    /**
     * 最新批次出入库状态：
     * 0-待确认（初始状态：等待确认备货/来料）
     * 1-待出入库（有确认数量：入库为待入库，出库为待出库，支持分批）
     * 2-出入库中（正在进行出入库操作：入库为入库中，出库为出库中）
     * 3-待生产确认（仅生产相关出库单据：SCLL、SCBL、SCRK、SCTL）
     * 4-生产已确认（生产确认完成）
     * 5-待仓库确认（等待仓库最终确认）
     * 6-仓库已确认（仓库确认完成）
     * 7-已完成（最终完成状态）
     */
    private Integer latestWarehouseStatus;

    // ==================== 出库单据专用字段 ====================

    /**
     * 容器编码（仅出库单据使用）
     * 出库场景：指定出库的容器编码
     * 入库场景：此字段为空
     */
    private String containerCode;

    /**
     * 批次库存ID（仅出库单据使用）
     * 出库场景：关联的批次库存记录ID，用于精确指定出库的库存批次
     * 入库场景：此字段为空
     */
    private String inventoryId;

    /**
     * 容器出库数量（仅出库单据使用）
     * 出库场景：需要从该容器的出库数量（等于 DocumentDetailDto.quantity）
     * 注：同一物料的多个容器的 containerQuantity 之和应等于 quantity（物料总需求数量）
     */
    private Integer containerQuantity;

    /**
     * 容器累计已确认数量（该容器的累计确认数量，仅出库单据使用）
     * 出库场景：跟踪指定容器的累计确认进度，支持容器分批确认
     * 注：当同一容器分多次确认时，此字段记录该容器的累计确认数量
     * 计算方式：每次确认该容器时累加确认数量
     */
    private Integer containerTotalConfirmed;

}