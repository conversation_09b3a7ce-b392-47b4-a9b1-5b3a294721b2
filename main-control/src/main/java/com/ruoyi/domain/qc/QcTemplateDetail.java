package com.ruoyi.domain.qc;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0
 * @Description: [质检模板-详情]
 * @date 2024/6/4 17:30
 */
@Data
@TableName("qc_template_detail")
public class QcTemplateDetail {
    private String id;

    /**
     * 检验模板编码
     */
    private String templateCode;

    /**
     * 检测项编码
     */
    private String itemCode;

    /**
     * 检测要求
     */
    private  String checkMethod;
    /**
     * 标准值
     */
    private  String standerVal;
    /**
     * 单位
     */
    private  String unitOfMeasure;
    /**
     * 误差上限
     */
    private  String thresholdMax;
    /**
     * 误差下限
     */
    private  String thresholdMin;
    /**
     * 说明图
     */
    private  String docUrl;
    /**
     * 备注
     */
    private  String remark;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
