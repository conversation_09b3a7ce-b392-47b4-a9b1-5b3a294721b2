package com.ruoyi.mapper.basicData;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.basicData.DocumentInventoryDetail;
import com.ruoyi.utils.DocumentInventoryDetailQueryVO;
import com.ruoyi.vo.basicData.DocumentInventoryDetailDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DocumentInventoryDetailMapper extends BaseMapper<DocumentInventoryDetail> {
    List<DocumentInventoryDetailDto> queryDocumentInventoryDetail(DocumentInventoryDetailQueryVO queryParamVO);

    /**
     * 根据单据明细编码查询批次记录
     * @param detailCode 单据明细编码
     * @return 批次记录列表
     */
    List<DocumentInventoryDetail> selectByDetailCode(@Param("detailCode") String detailCode);
}
