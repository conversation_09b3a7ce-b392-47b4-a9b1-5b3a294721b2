package com.ruoyi.controller.basicData;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.domain.basicData.BasicDocumentDetail;
import com.ruoyi.domain.basicData.BasicDocumentInfo;
import com.ruoyi.service.basicData.BasicDocumentDetailService;
import com.ruoyi.service.basicData.BasicDocumentInfoService;
import com.ruoyi.service.basicData.BasicMaterialBatchInventoryService;
import com.ruoyi.service.basicData.DocumentInventoryDetailService;
import com.ruoyi.service.document.DocumentDetailResponse;
import com.ruoyi.service.work.DocumentExecuteService;
import com.ruoyi.utils.*;
import com.ruoyi.vo.basicData.BasicDocumentInfoDto;
import com.ruoyi.vo.basicData.ConfirmArrivalRequest;
import com.ruoyi.vo.basicData.DocumentInventoryDetailDto;
import com.ruoyi.vo.document.DocumentInventoryConfirmVo;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 基础数据—单据管理
 */
@RestController
@RequestMapping("/speedbot/basicData/documentInfo")
public class DocumentInfoController extends BaseController {
    protected final Logger logger = LoggerFactory.getLogger(DocumentInfoController.class);

    @Resource
    private BasicDocumentInfoService basicDocumentInfoService;

    @Resource
    private BasicDocumentDetailService basicDocumentDetailService;

    @Resource
    private DocumentInventoryDetailService documentInventoryDetailService;

    @Resource
    DocumentExecuteService documentExecuteService;

    @Resource
    private BasicMaterialBatchInventoryService basicMaterialBatchInventoryService;

    /**
     * 查询单据（支持物料查询、供应商/客户名称查询等高级功能）
     * @param queryParamVO 增强查询参数
     * @return 查询结果
     */
    @PostMapping("/queryDocumentInfo")
    public TableDataInfo queryDocumentInfoEnhanced(@RequestBody DocumentQueryParamVO queryParamVO){
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<BasicDocumentInfo> list = this.basicDocumentInfoService.queryDocumentInfoEnhanced(queryParamVO);
        return this.getDataTable(list);
    }


    /**
     *
     * 单据新增
     */
    @PostMapping("/addDocumentInfo")
    @Log(title = "单据新增", businessType = BusinessType.INSERT)
    public ResponseResult addDocumentInfo(@RequestBody @Validated BasicDocumentInfoDto param ){
        logger.info("WEB单据新增接口：" + GsonUtils.toJsonString(param));
        return basicDocumentInfoService.addDocumentInfo(param);
    }

    /**
     * 单据更新
     */
    @PostMapping("/updateDocumentInfo")
    @Log(title = "单据更新", businessType = BusinessType.UPDATE)
    public ResponseResult updateDocumentInfo(@RequestBody @Validated BasicDocumentInfoDto param ){
        return basicDocumentInfoService.updateDocumentInfo(param);
    }

    /**
     * 单据删除
     */
    @Log(title = "单据删除", businessType = BusinessType.DELETE)
    @PostMapping("/delDocumentInfo")
    public ResponseResult delDocumentInfo(@RequestBody BatchIdsReq req){
        return this.basicDocumentInfoService.delDocumentInfo(req);
    }


    /**
     * 查询单据详情
     */
    @PostMapping("/queryDocumentDetail")
    public TableDataInfo queryDocumentDetail(@RequestBody QueryParamVO queryParamVO){
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<DocumentDetailResponse> list = basicDocumentDetailService.queryDocumentDetail(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     *
     * 单据详情新增
     */
    @PostMapping("/addDocumentDetail")
    @Log(title = "单据详情新增", businessType = BusinessType.INSERT)
    public ResponseResult addDocumentDetail(@RequestBody @Validated BasicDocumentDetail param ){
        return basicDocumentDetailService.addDocumentDetail(param);
    }

    /**
     * 单据详情更新
     */
    @PostMapping("/updateDocumentDetail")
    @Log(title = "单据详情更新", businessType = BusinessType.UPDATE)
    public ResponseResult updateDocumentDetail(@RequestBody @Validated BasicDocumentDetail param ){
        return basicDocumentDetailService.updateDocumentDetail(param);
    }

    /**
     * 单据详情删除
     */
    @PostMapping("/deleteDocumentDetail")
    @Log(title = "单据详情删除", businessType = BusinessType.DELETE)
    public ResponseResult deleteDocumentDetail(@RequestBody BatchIdsReq req ){
        return basicDocumentDetailService.deleteDocumentDetail(req);
    }


    /**
     * 查询单据物料明细
     */
    @PostMapping("/queryDocumentInventoryDetail")
    public TableDataInfo queryDocumentInventoryDetail(@RequestBody DocumentInventoryDetailQueryVO queryParamVO){
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<DocumentInventoryDetailDto> list = documentInventoryDetailService.queryDocumentInventoryDetail(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 单据出入库确认
     * description: 确认本次出入库的实际数量
     * @author: sqpeng
     * @param param 出入库确认请求
     * @return {@link ResponseResult}
     */
    @PostMapping("/documentInventoryInOut")
    @Log(title = "单据出入库确认", businessType = BusinessType.UPDATE)
    public ResponseResult documentInventoryInOut(@RequestBody @Validated DocumentInventoryConfirmVo param) {
        return documentExecuteService.documentInventoryConfirm(param);
    }

//    /**
//     * 单据出入库（旧版本，保持兼容性）
//     * @deprecated 请使用新版本的documentInventoryInOut接口
//     */
//    @PostMapping("/documentInventoryInOutOld")
//    @Log(title = "单据出入库（旧版）", businessType = BusinessType.INSERT)
//    @Deprecated
//    public ResponseResult documentInventoryInOutOld(@RequestBody DocumentInventoryVo param) {
//        return documentExecuteService.documentInventoryInOut(param);
//    }

    /**
     * 确认到货(来料确认、出货确认）
     * description: 确认单据的到货数量，生成批次记录和质检任务
     * 入库场景：确认入库单据的来料数量
     * 出库场景：确认出库单据的备货数量
     * @author: sqpeng
     * @param request 确认到货请求
     * @return {@link ResponseResult}
     */
    @PostMapping("/confirmArrival")
    @Log(title = "确认到货", businessType = BusinessType.UPDATE)
    public ResponseResult confirmArrival(@RequestBody @Validated ConfirmArrivalRequest request) {
        try {
            logger.info("收到确认到货请求，单据ID：{}，到货明细数量：{}", request.getDocumentId(), request.getArrivalList().size());
            return basicDocumentInfoService.confirmArrival(request.getDocumentId(), request.getArrivalList());
        } catch (Exception e) {
            logger.info("确认到货失败，单据ID：{}", request.getDocumentId());
            return ResponseResult.getErrorResult("确认到货失败：" + e.getMessage());
        }
    }


}
